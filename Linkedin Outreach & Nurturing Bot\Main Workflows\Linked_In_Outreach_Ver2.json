{"name": "Linked In Outreach Ver2", "nodes": [{"parameters": {"rule": {"interval": [{"triggerAtHour": 12}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-620, 220], "id": "41cbb95e-e63a-4aa5-82a9-c790ce226e0b", "name": "Schedule Trigger"}, {"parameters": {"resource": "databasePage", "operation": "getAll", "databaseId": {"__rl": true, "value": "208a7d56-8092-80cc-9112-ef46e9114424", "mode": "list", "cachedResultName": "LinkedIn Outreach", "cachedResultUrl": "https://www.notion.so/208a7d56809280cc9112ef46e9114424"}, "returnAll": true, "filterType": "manual", "filters": {"conditions": [{"key": "Status|status", "condition": "equals", "statusValue": "Added"}]}, "options": {}}, "type": "n8n-nodes-base.notion", "typeVersion": 2.2, "position": [-100, -200], "id": "5199a9b4-ce2d-499e-81af-bad971011e48", "name": "Notion", "credentials": {"notionApi": {"id": "2dGJhkNEGUxbFEcj", "name": "Notion account 2"}}}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [220, -200], "id": "809e906e-1385-407f-8b7c-490022aebc03", "name": "Loop Over Items"}, {"parameters": {"method": "POST", "url": "https://api.phantombuster.com/api/v2/agents/launch", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Phantombuster-Key", "value": "NNgyL6b87toLVhY58VaH8KrFv4e3NncldG9tVmGGd50"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"id\": \"****************\",\n          \"arguments\":\n{\n\t\"numberOfAddsPerLaunch\": 10,\n\t\"onlySecondCircle\": false,\n\t\"dwellTime\": true,\n\t\"inputType\": \"profileUrl\",\n\t\"profileUrl\": \"{{ $json.property_linked_in_url }}\",\n\t\"identityId\": \"****************\",\n\t\"sessionCookie\": \"AQEDAVrxdR4A2X44AAABlvI2szIAAAGXOkww7U4AxIknISQXSKYyJ7RtJo4RSwv92tAZBSZGST58vonZvbUWq6xR17yIVSNdHFv8-PlC2-8DjXFJLZxObZxh0S10OoGRHHIiXGUUl-SsZQ6MmgNhG-Ws\",\n\t\"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36\",\n\t\"message\": \"Hey #firstName#, Kindly accept my connection request\",\n\t\"emailChooser\": \"none\",\n\t\"enableScraping\": false\n},\n          \"saveArguments\": false\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [720, -60], "id": "2f2411a4-59d2-4b2f-9c1d-cb0fcf9d84cd", "name": "Phantombuster - Launch LinkedIn Profile Scraper phantom"}, {"parameters": {"amount": 100}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [500, -60], "id": "d943b4a2-3fed-435e-b1c1-9a07f324b2a8", "name": "Wait for Phantombuster rate limit", "webhookId": "effbaad6-d520-4b35-9298-88494c86ad34"}, {"parameters": {"url": "https://api.phantombuster.com/api/v2/containers/fetch-result-object", "sendQuery": true, "queryParameters": {"parameters": [{"name": "id", "value": "={{ $json.containerId }}"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Phantombuster-Key", "value": "NNgyL6b87toLVhY58VaH8KrFv4e3NncldG9tVmGGd50"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [720, -240], "id": "5c558249-e393-4292-9100-e140a96f9fb7", "name": "Phantombuster - Get Profiles Info"}, {"parameters": {"amount": 100}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [500, -240], "id": "8abb5eef-deac-45d6-b949-319b461e085d", "name": "Wait for Phantombuster rate limit 2", "webhookId": "81c48df6-e61f-4114-a0f1-8afedc5d2179"}, {"parameters": {"assignments": {"assignments": [{"id": "3e145973-2c8f-44ad-8765-09cd85f18655", "name": "output", "value": "={{JSON.parse( $json.resultObject) }}", "type": "array"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [900, -200], "id": "46dfc5c3-f96b-4bab-bc7a-5132e7e6c75c", "name": "parse string to json"}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [1120, -180], "id": "f0488482-bf83-45b1-93ee-f54f2f408fc3", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "# Send Connection Requests\n## - send linkedin urls to phantombuster to swnd to them connection requests\n## - Connection is made through looping and waiting between each url to avoice rate limiting issue", "height": 540, "width": 1560, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [100, -400], "id": "6371def2-b891-42f9-8dbd-bdf10e8d8f07", "name": "Sticky Note2"}, {"parameters": {"resource": "databasePage", "operation": "update", "pageId": {"__rl": true, "value": "={{ $json.url }}", "mode": "url"}, "propertiesUi": {"propertyValues": [{"key": "Status|status", "statusValue": "Pending"}, {"key": "Accepted date|date", "date": "2025-06-04T09:10:46"}]}, "options": {}}, "type": "n8n-nodes-base.notion", "typeVersion": 2.2, "position": [1540, -200], "id": "111784cf-c6de-4892-9c07-cead254953cb", "name": "Notion1", "credentials": {"notionApi": {"id": "2dGJhkNEGUxbFEcj", "name": "Notion account 2"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "5ac4e7eb-8cfb-4937-a606-3c5317a5ecf8", "leftValue": "={{ $json.output[0].error }}", "rightValue": "", "operator": {"type": "string", "operation": "notExists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [1320, -200], "id": "376c55fa-dcfb-4f3a-96cf-53f8ca51b003", "name": "Filter"}, {"parameters": {"resource": "databasePage", "operation": "getAll", "databaseId": {"__rl": true, "value": "208a7d56-8092-80cc-9112-ef46e9114424", "mode": "list", "cachedResultName": "LinkedIn Outreach", "cachedResultUrl": "https://www.notion.so/208a7d56809280cc9112ef46e9114424"}, "filterType": "manual", "filters": {"conditions": [{"key": "Status|status", "condition": "equals", "statusValue": "Pending"}, {"key": "Status|status", "condition": "equals", "statusValue": "Ignored"}]}, "options": {}}, "type": "n8n-nodes-base.notion", "typeVersion": 2.2, "position": [120, 420], "id": "4a7e42c9-d7d8-40e9-bbff-9f3ff04eafcd", "name": "Notion2", "credentials": {"notionApi": {"id": "2dGJhkNEGUxbFEcj", "name": "Notion account 2"}}}, {"parameters": {"method": "POST", "url": "https://api.phantombuster.com/api/v2/agents/launch", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Phantombuster-Key", "value": "NNgyL6b87toLVhY58VaH8KrFv4e3NncldG9tVmGGd50"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"id\": \"****************\",\n          \"arguments\": {\n\t\"numberOfProfiles\": 50,\n\t\"sortBy\": \"Recently added\",\n\t\"sessionCookie\": \"AQEDAVrxdR4A2X44AAABlvI2szIAAAGXOkww7U4AxIknISQXSKYyJ7RtJo4RSwv92tAZBSZGST58vonZvbUWq6xR17yIVSNdHFv8-PlC2-8DjXFJLZxObZxh0S10OoGRHHIiXGUUl-SsZQ6MmgNhG-Ws\",\n\t\"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36\"\n},\n          \"saveArguments\": false\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [120, 620], "id": "2d002333-9639-4d67-b5f5-87f1932ba743", "name": "Phantombuster - Launch LinkedIn Profile Scraper phantom1"}, {"parameters": {"url": "https://api.phantombuster.com/api/v2/containers/fetch-result-object", "sendQuery": true, "queryParameters": {"parameters": [{"name": "id", "value": "={{ $json.containerId }}"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Phantombuster-Key", "value": "NNgyL6b87toLVhY58VaH8KrFv4e3NncldG9tVmGGd50"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [520, 620], "id": "494c6d11-9486-4453-9aa3-01347da51305", "name": "Phantombuster - Get Profiles Info1"}, {"parameters": {"assignments": {"assignments": [{"id": "3e145973-2c8f-44ad-8765-09cd85f18655", "name": "output", "value": "={{JSON.parse( $json.resultObject) }}", "type": "array"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [700, 620], "id": "ac893969-1f86-479c-8f95-dafe920ea59e", "name": "parse string to json1"}, {"parameters": {"amount": 100}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [320, 620], "id": "82cbcb4e-e1dc-4d4c-b78d-4193707d6617", "name": "Wait for Phantombuster rate limit ", "webhookId": "81c48df6-e61f-4114-a0f1-8afedc5d2179"}, {"parameters": {"mode": "combine", "advanced": true, "mergeByFields": {"values": [{"field1": "property_linked_in_url", "field2": "output[0].profileUrl"}]}, "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [940, 440], "id": "54ce8558-dd8d-4aeb-9eb9-c21f614ebda7", "name": "Merge1"}, {"parameters": {"jsCode": "// Loop over input items\nfor (const item of $input.all()) {\n  const url = item.json.property_linked_in_url;\n\n  if (url && typeof url === 'string') {\n    // Replace \"https://linkedin.com\" with \"https://www.linkedin.com\"\n    item.json.property_linked_in_url = url.replace(\n      /^https:\\/\\/linkedin\\.com/,\n      'https://www.linkedin.com'\n    );\n  }\n}\n\nreturn $input.all();\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [480, 420], "id": "38550d44-8c34-40db-8d0b-aea6bf614b41", "name": "Code"}, {"parameters": {"resource": "databasePage", "operation": "update", "pageId": {"__rl": true, "value": "={{ $json.url }}", "mode": "url"}, "propertiesUi": {"propertyValues": [{"key": "Status|status", "statusValue": "Connected"}, {"key": "Accepted date|date", "date": "2025-06-04T08:09:39"}]}, "options": {}}, "type": "n8n-nodes-base.notion", "typeVersion": 2.2, "position": [1260, 440], "id": "2519e504-664b-4841-834d-e6afe9cc9783", "name": "Notion3", "credentials": {"notionApi": {"id": "2dGJhkNEGUxbFEcj", "name": "Notion account 2"}}}, {"parameters": {"resource": "databasePage", "operation": "update", "pageId": {"__rl": true, "value": "={{ $json.url }}", "mode": "url"}, "propertiesUi": {"propertyValues": [{"key": "Status|status", "statusValue": "Ignored"}]}, "options": {}}, "type": "n8n-nodes-base.notion", "typeVersion": 2.2, "position": [1420, 600], "id": "356b6585-dfc7-4c99-9386-5774b4a4f313", "name": "Notion4", "credentials": {"notionApi": {"id": "2dGJhkNEGUxbFEcj", "name": "Notion account 2"}}}, {"parameters": {"mode": "combine", "advanced": true, "mergeByFields": {"values": [{"field1": "property_linked_in_url", "field2": "output[0].profileUrl"}]}, "joinMode": "keepNonMatches", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [940, 600], "id": "d6c7db15-4b41-4f2b-a3ef-09006bab989d", "name": "Merge2"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "2301bc01-546a-48f7-a223-827f1e613072", "leftValue": "={{ $json.property_accepted_date.start }}", "rightValue": "={{ new Date(Date.now() - ********) }}", "operator": {"type": "dateTime", "operation": "before"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [1180, 600], "id": "749268f7-47f8-4f19-a726-039b1bcbd89f", "name": "Filter1"}, {"parameters": {"content": "# Check connection status\n## - if a user accepted the connection request then he is flagged connected\n## - else he is flagged ignored", "height": 540, "width": 1560, "color": 2}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [100, 240], "id": "2240a600-2bcf-4922-8a8c-cf5a08a3577b", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "# Send a follow up message & update notion\n## - if a user accepted the connection request then he is flagged connected\n## - else he is flagged ignored<PERSON><PERSON> e inonon", "height": 580, "width": 1920}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [60, 1000], "id": "b5016cf8-7c99-4af5-b504-af8dc15b794c", "name": "Sticky Note1"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [380, 1220], "id": "77cd8310-6ff3-4ae9-8436-c772de13233b", "name": "Loop Over Items1"}, {"parameters": {"amount": 100}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [580, 1380], "id": "a7e141f7-8d23-4511-a178-633b09c7c085", "name": "Wait for Phantombuster rate limit1", "webhookId": "effbaad6-d520-4b35-9298-88494c86ad34"}, {"parameters": {"url": "https://api.phantombuster.com/api/v2/containers/fetch-result-object", "sendQuery": true, "queryParameters": {"parameters": [{"name": "id", "value": "={{ $json.containerId }}"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Phantombuster-Key", "value": "NNgyL6b87toLVhY58VaH8KrFv4e3NncldG9tVmGGd50"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [940, 1200], "id": "97bb62d0-3517-4c51-8f3e-121a5966a656", "name": "Phantombuster -GET Result of Send a follow up message"}, {"parameters": {"method": "POST", "url": "https://api.phantombuster.com/api/v2/agents/launch", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Phantombuster-Key", "value": "NNgyL6b87toLVhY58VaH8KrFv4e3NncldG9tVmGGd50"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"id\": \"1328917266075078\",\n          \"arguments\": \n{\n\t\"spreadsheetUrl\": \"{{ $json.property_linked_in_url }}\",\n\t\"spreadsheetUrlExclusionList\": [],\n\t\"sessionCookie\": \"AQEDAVrxdR4A2X44AAABlvI2szIAAAGXOkww7U4AxIknISQXSKYyJ7RtJo4RSwv92tAZBSZGST58vonZvbUWq6xR17yIVSNdHFv8-PlC2-8DjXFJLZxObZxh0S10OoGRHHIiXGUUl-SsZQ6MmgNhG-Ws\",\n\t\"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36\",\n\t\"messageControl\": \"none\",\n\t\"message\": \"Hey #firstName#, wanted to share a quick guide we created on automation ROI for startups—happy to hear your thoughts\",\n\t\"emailChooser\": \"none\"\n},\n          \"saveArguments\": false\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [800, 1380], "id": "82af3ef9-221d-4500-ac87-ed27da05d0b8", "name": "Phantombuster - Send a follow up message"}, {"parameters": {"assignments": {"assignments": [{"id": "3e145973-2c8f-44ad-8765-09cd85f18655", "name": "output", "value": "={{JSON.parse( $json.resultObject) }}", "type": "array"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1200, 1200], "id": "0df212ad-c032-4ddb-8192-c61b06d59cf6", "name": "parse string to json2"}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [1420, 1060], "id": "387d37c2-71f5-4486-acf2-2bf41501ba19", "name": "Merge3"}, {"parameters": {"resource": "databasePage", "operation": "update", "pageId": {"__rl": true, "value": "={{ $json.url }}", "mode": "url"}, "propertiesUi": {"propertyValues": [{"key": "Note|rich_text", "textContent": "={{ $json.output[0].message }}"}]}, "options": {}}, "type": "n8n-nodes-base.notion", "typeVersion": 2.2, "position": [1620, 1060], "id": "c304bbe5-87fa-4278-b7ee-70573f4742f4", "name": "Update Notion with last message", "credentials": {"notionApi": {"id": "2dGJhkNEGUxbFEcj", "name": "Notion account 2"}}}, {"parameters": {"content": "# Get user reply & update notion\n## - \n## - ", "height": 560, "width": 2440, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [40, 1780], "id": "a6ebfe69-c456-4ccd-b6ac-7229f6e25a57", "name": "Sticky Note3"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [380, 1980], "id": "3361d0fa-4879-41bf-ac8b-4ac1e0b54044", "name": "Loop Over Items2"}, {"parameters": {"amount": 100}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [580, 2140], "id": "912be5c7-36a4-43af-9105-ade3acab2ad1", "name": "Wait for Phantombuster rate limit2", "webhookId": "effbaad6-d520-4b35-9298-88494c86ad34"}, {"parameters": {"assignments": {"assignments": [{"id": "3e145973-2c8f-44ad-8765-09cd85f18655", "name": "output", "value": "={{JSON.parse( $json.resultObject) }}", "type": "array"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1200, 1960], "id": "833c791a-432f-4b16-b54b-6a23422f014e", "name": "parse string to json3"}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [1940, 1840], "id": "421c5d1e-5189-42ad-96a8-2338f8f500ef", "name": "Merge4"}, {"parameters": {"method": "POST", "url": "https://api.phantombuster.com/api/v2/agents/launch", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Phantombuster-Key", "value": "NNgyL6b87toLVhY58VaH8KrFv4e3NncldG9tVmGGd50"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"id\": \"5380766101384651\",\n          \"arguments\": \n{\n\t\"profilesPerLaunch\": 5,\n\t\"messagesPerExtract\": 5,\n\t\"chronOrder\": false,\n\t\"spreadsheetUrl\": \"{{ $json.property_linked_in_url }}\",\n\t\"sessionCookie\": \"AQEDAVrxdR4A2X44AAABlvI2szIAAAGXOkww7U4AxIknISQXSKYyJ7RtJo4RSwv92tAZBSZGST58vonZvbUWq6xR17yIVSNdHFv8-PlC2-8DjXFJLZxObZxh0S10OoGRHHIiXGUUl-SsZQ6MmgNhG-Ws\",\n\t\"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36\",\n\t\"noDatabase\": true\n},\n          \"saveArguments\": false\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [800, 2140], "id": "dd97d3c0-e534-4089-8210-b5283ccedbcb", "name": "Phantombuster - GET User replies messages"}, {"parameters": {"url": "https://api.phantombuster.com/api/v2/containers/fetch-result-object", "sendQuery": true, "queryParameters": {"parameters": [{"name": "id", "value": "={{ $json.containerId }}"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Phantombuster-Key", "value": "NNgyL6b87toLVhY58VaH8KrFv4e3NncldG9tVmGGd50"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [940, 1960], "id": "e7372af1-f4ff-4e72-bf4b-6e785a229503", "name": "Phantombuster - GET Result of GET User replies messages"}, {"parameters": {"amount": 100}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [680, 1200], "id": "48ff462c-ae20-4734-b391-b100fe052329", "name": "Wait for Phantombuster rate limit3", "webhookId": "effbaad6-d520-4b35-9298-88494c86ad34"}, {"parameters": {"amount": 100}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [660, 1960], "id": "d9a3322f-bc15-4e1b-9d0f-683d0fcb8028", "name": "Wait for Phantombuster rate limit4", "webhookId": "effbaad6-d520-4b35-9298-88494c86ad34"}, {"parameters": {"resource": "databasePage", "operation": "update", "pageId": {"__rl": true, "value": "={{ $json.url }}", "mode": "url"}, "propertiesUi": {"propertyValues": [{"key": "Note|rich_text", "textContent": "={{ $json.lastMessage }}"}, {"key": "Status|status", "statusValue": "={{ $json.userReplied ? \"Replied\" : \"Connected\" }}"}, {"key": "Engagement|status", "statusValue": "={{ $json.output }}"}]}, "options": {}}, "type": "n8n-nodes-base.notion", "typeVersion": 2.2, "position": [2300, 1780], "id": "f4ec721e-7235-4bb1-b0b6-849de479df87", "name": "Update Notion with User reply", "credentials": {"notionApi": {"id": "2dGJhkNEGUxbFEcj", "name": "Notion account 2"}}}, {"parameters": {"resource": "databasePage", "operation": "getAll", "databaseId": {"__rl": true, "value": "208a7d56-8092-80cc-9112-ef46e9114424", "mode": "list", "cachedResultName": "LinkedIn Outreach", "cachedResultUrl": "https://www.notion.so/208a7d56809280cc9112ef46e9114424"}, "returnAll": true, "filterType": "manual", "matchType": "allFilters", "filters": {"conditions": [{"key": "Status|status", "condition": "equals", "statusValue": "Connected"}, {"key": "Accepted date|date", "condition": "before", "date": "={{ new Date(Date.now() - ********).toISOString().split('T')[0] }}"}, {"key": "Note|rich_text", "condition": "is_empty"}]}, "options": {}}, "type": "n8n-nodes-base.notion", "typeVersion": 2.2, "position": [120, 1220], "id": "45db0077-96ec-4126-9202-207c23d79ca8", "name": "Notion5", "credentials": {"notionApi": {"id": "2dGJhkNEGUxbFEcj", "name": "Notion account 2"}}}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n  item.json.myNewField = 1;\n  let UserReplied = false;\n  for(const msg of item.json.output[0].messages)\n    {\n      if(msg.connectionDegree != \"You\")\n      {\n        UserReplied = true;\n        break;\n      }\n    }\n  if(UserReplied)\n  {\n    item.json.userReplied = UserReplied;\n  }\n  item.json.lastMessage = item.json.output[0].messages[0].message; \n}\n\nreturn $input.all();"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1420, 1960], "id": "28c96fd1-bbc4-49a0-a554-95a8ea09dd60", "name": "Code1"}, {"parameters": {"resource": "databasePage", "operation": "getAll", "databaseId": {"__rl": true, "value": "208a7d56-8092-80cc-9112-ef46e9114424", "mode": "list", "cachedResultName": "LinkedIn Outreach", "cachedResultUrl": "https://www.notion.so/208a7d56809280cc9112ef46e9114424"}, "returnAll": true, "filterType": "manual", "matchType": "allFilters", "filters": {"conditions": [{"key": "Status|status", "condition": "equals", "statusValue": "Connected"}, {"key": "Accepted date|date", "condition": "before", "date": "={{ new Date(Date.now() - ******** * 2).toISOString().split('T')[0] }}"}]}, "options": {}}, "type": "n8n-nodes-base.notion", "typeVersion": 2.2, "position": [80, 1980], "id": "5d414ad2-7ee8-4a84-99a1-33c01f00eadf", "name": "Notion6", "credentials": {"notionApi": {"id": "2dGJhkNEGUxbFEcj", "name": "Notion account 2"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.output[0].messages }}", "options": {"systemMessage": "You are an Engagement Evaluator AI. Analyze a series of user messages and determine the level of engagement based on who initiates and maintains the conversation.\n\nIf the user is the only one sending messages and receives no replies, return \"Unread\".\n\nIf both parties are messaging, but the user sends messages infrequently or inconsistently, return \"Neutral\".\n\nIf the majority of messages come from the other party (the connection), indicating they are actively engaging with the user, return \"Positive\".\n\nYour response must be one of the following exact words: Unread, Neutral, or Positive."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [1640, 1960], "id": "ebff5458-bbea-4883-8f50-7ef3a0dbd7dc", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1640, 2140], "id": "b3cb6003-bbdb-4d72-af53-36b921788480", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "n6j811BvFHF0g3jI", "name": "OpenAi account"}}}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [2100, 1780], "id": "501a032e-7640-4896-b6e6-7bc43bc7b1f9", "name": "Merge5"}], "pinData": {}, "connections": {"Schedule Trigger": {"main": [[{"node": "Notion6", "type": "main", "index": 0}, {"node": "Notion5", "type": "main", "index": 0}, {"node": "Phantombuster - Launch LinkedIn Profile Scraper phantom1", "type": "main", "index": 0}, {"node": "Notion2", "type": "main", "index": 0}, {"node": "Notion", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Wait for Phantombuster rate limit 2", "type": "main", "index": 0}], [{"node": "Wait for Phantombuster rate limit", "type": "main", "index": 0}]]}, "Phantombuster - Launch LinkedIn Profile Scraper phantom": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Wait for Phantombuster rate limit": {"main": [[{"node": "Phantombuster - Launch LinkedIn Profile Scraper phantom", "type": "main", "index": 0}]]}, "Notion": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Wait for Phantombuster rate limit 2": {"main": [[{"node": "Phantombuster - Get Profiles Info", "type": "main", "index": 0}]]}, "Phantombuster - Get Profiles Info": {"main": [[{"node": "parse string to json", "type": "main", "index": 0}]]}, "parse string to json": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Merge": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}, "Filter": {"main": [[{"node": "Notion1", "type": "main", "index": 0}]]}, "Phantombuster - Launch LinkedIn Profile Scraper phantom1": {"main": [[{"node": "Wait for Phantombuster rate limit ", "type": "main", "index": 0}]]}, "Phantombuster - Get Profiles Info1": {"main": [[{"node": "parse string to json1", "type": "main", "index": 0}]]}, "Notion2": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Wait for Phantombuster rate limit ": {"main": [[{"node": "Phantombuster - Get Profiles Info1", "type": "main", "index": 0}]]}, "parse string to json1": {"main": [[{"node": "Merge1", "type": "main", "index": 1}, {"node": "Merge2", "type": "main", "index": 1}]]}, "Merge1": {"main": [[{"node": "Notion3", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Merge1", "type": "main", "index": 0}, {"node": "Merge2", "type": "main", "index": 0}]]}, "Merge2": {"main": [[{"node": "Filter1", "type": "main", "index": 0}]]}, "Filter1": {"main": [[{"node": "Notion4", "type": "main", "index": 0}]]}, "Notion3": {"main": [[]]}, "Loop Over Items1": {"main": [[{"node": "Wait for Phantombuster rate limit3", "type": "main", "index": 0}], [{"node": "Wait for Phantombuster rate limit1", "type": "main", "index": 0}]]}, "Wait for Phantombuster rate limit1": {"main": [[{"node": "Phantombuster - Send a follow up message", "type": "main", "index": 0}]]}, "Phantombuster - Send a follow up message": {"main": [[{"node": "Loop Over Items1", "type": "main", "index": 0}]]}, "parse string to json2": {"main": [[{"node": "Merge3", "type": "main", "index": 1}]]}, "Phantombuster -GET Result of Send a follow up message": {"main": [[{"node": "parse string to json2", "type": "main", "index": 0}]]}, "Notion4": {"main": [[]]}, "Merge3": {"main": [[{"node": "Update Notion with last message", "type": "main", "index": 0}]]}, "Update Notion with last message": {"main": [[]]}, "Loop Over Items2": {"main": [[{"node": "Wait for Phantombuster rate limit4", "type": "main", "index": 0}], [{"node": "Wait for Phantombuster rate limit2", "type": "main", "index": 0}]]}, "Wait for Phantombuster rate limit2": {"main": [[{"node": "Phantombuster - GET User replies messages", "type": "main", "index": 0}]]}, "parse string to json3": {"main": [[{"node": "Code1", "type": "main", "index": 0}]]}, "Merge4": {"main": [[{"node": "Merge5", "type": "main", "index": 1}]]}, "Phantombuster - GET User replies messages": {"main": [[{"node": "Loop Over Items2", "type": "main", "index": 0}]]}, "Phantombuster - GET Result of GET User replies messages": {"main": [[{"node": "parse string to json3", "type": "main", "index": 0}]]}, "Wait for Phantombuster rate limit3": {"main": [[{"node": "Phantombuster -GET Result of Send a follow up message", "type": "main", "index": 0}]]}, "Wait for Phantombuster rate limit4": {"main": [[{"node": "Phantombuster - GET Result of GET User replies messages", "type": "main", "index": 0}]]}, "Notion5": {"main": [[{"node": "Loop Over Items1", "type": "main", "index": 0}, {"node": "Merge3", "type": "main", "index": 0}]]}, "Code1": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}, {"node": "Merge4", "type": "main", "index": 0}]]}, "Notion6": {"main": [[{"node": "Loop Over Items2", "type": "main", "index": 0}, {"node": "Merge5", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Merge4", "type": "main", "index": 1}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Merge5": {"main": [[{"node": "Update Notion with User reply", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner", "executionTimeout": -1, "errorWorkflow": "QjfQ9CWNToFgyvDh"}, "versionId": "870e5818-002e-47fd-af4d-b7112ebf904a", "meta": {"templateCredsSetupCompleted": true, "instanceId": "36e066b6e7fd7141a4037cea4720adcd14b55b97aa9443f1eac8dd561c769073"}, "id": "5QytXGgtKswwNkou", "tags": []}